import axios from 'axios';
import { Logger } from '../utils/Logger.js';

export class RAGService {
  constructor() {
    this.logger = new Logger();
    this.frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    this.ragApiUrl = process.env.RAG_API_URL || 'http://localhost:3000/api';
    this.isInitialized = false;
    
    // Configuração do axios
    this.httpClient = axios.create({
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vereadora-Rafaela-WhatsApp-Backend/1.0.0'
      }
    });

    // Configurar interceptors
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug(`🔄 Fazendo requisição para: ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('❌ Erro na requisição:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`✅ Resposta recebida de: ${response.config.url}`);
        return response;
      },
      (error) => {
        this.logger.error('❌ Erro na resposta:', error.message);
        return Promise.reject(error);
      }
    );
  }

  async initialize() {
    this.logger.info('🔄 Inicializando RAG Service...');
    
    try {
      // Testar conexão com o frontend
      await this.testConnection();
      this.isInitialized = true;
      this.logger.info('✅ RAG Service inicializado com sucesso!');
      return true;
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar RAG Service:', error);
      // Não falhar completamente - pode funcionar em modo offline
      this.isInitialized = false;
      return false;
    }
  }

  async testConnection() {
    try {
      const response = await this.httpClient.get(`${this.frontendUrl}/`);
      this.logger.info('✅ Conexão com frontend estabelecida');
      return response.data;
    } catch (error) {
      this.logger.warn('⚠️ Não foi possível conectar com o frontend');
      throw error;
    }
  }

  async processMessage(userMessage, userInfo = {}) {
    try {
      this.logger.info(`🤖 Processando mensagem RAG: "${userMessage.substring(0, 50)}..."`);

      // Se não estiver inicializado, usar resposta de fallback
      if (!this.isInitialized) {
        return this.getFallbackResponse(userMessage, userInfo);
      }

      // Preparar dados da mensagem
      const messageData = {
        message: userMessage,
        user: {
          phone: userInfo.phone || 'unknown',
          name: userInfo.name || 'Cidadão',
          timestamp: new Date().toISOString(),
          platform: 'whatsapp'
        },
        context: {
          isWhatsApp: true,
          sessionId: userInfo.sessionId || 'whatsapp-session',
          conversationId: userInfo.conversationId || null
        }
      };

      // Fazer requisição para o sistema RAG
      const response = await this.httpClient.post(`${this.ragApiUrl}/chat`, messageData);
      
      if (response.data && response.data.answer) {
        const ragResponse = {
          answer: response.data.answer,
          sources: response.data.sources || [],
          confidence: response.data.confidence || 0.8,
          processing_time: response.data.processing_time || 0,
          isFromRAG: true
        };

        this.logger.info(`✅ Resposta RAG gerada (confiança: ${Math.round(ragResponse.confidence * 100)}%)`);
        return this.formatWhatsAppResponse(ragResponse, userInfo);
      } else {
        throw new Error('Resposta inválida do sistema RAG');
      }

    } catch (error) {
      this.logger.error('❌ Erro ao processar mensagem RAG:', error);
      return this.getFallbackResponse(userMessage, userInfo);
    }
  }

  formatWhatsAppResponse(ragResponse, userInfo = {}) {
    const userName = userInfo.name || 'Cidadão';
    const isBusinessHours = this.isBusinessHours();
    
    let formattedMessage = '';

    // Saudação personalizada
    if (this.shouldIncludeGreeting(userInfo)) {
      const greeting = this.getGreeting();
      formattedMessage += `${greeting} ${userName}! 👋\n\n`;
    }

    // Resposta principal
    formattedMessage += ragResponse.answer;

    // Adicionar fontes se disponíveis
    if (ragResponse.sources && ragResponse.sources.length > 0) {
      formattedMessage += '\n\n📚 *Fontes consultadas:*\n';
      ragResponse.sources.slice(0, 2).forEach((source, index) => {
        formattedMessage += `${index + 1}. ${source.title}\n`;
      });
    }

    // Rodapé institucional
    formattedMessage += this.getFooter(isBusinessHours);

    return {
      message: formattedMessage,
      confidence: ragResponse.confidence,
      sources: ragResponse.sources,
      isFromRAG: true,
      timestamp: new Date().toISOString()
    };
  }

  getFallbackResponse(userMessage, userInfo = {}) {
    const userName = userInfo.name || 'Cidadão';
    const isBusinessHours = this.isBusinessHours();
    
    // Respostas baseadas em palavras-chave
    const responses = this.getFallbackResponses();
    const messageKey = userMessage.toLowerCase();
    
    let answer = responses.default;
    
    // Buscar resposta baseada em palavras-chave
    for (const [keywords, response] of Object.entries(responses.keywords)) {
      if (keywords.split(',').some(keyword => messageKey.includes(keyword.trim()))) {
        answer = response;
        break;
      }
    }

    const greeting = this.getGreeting();
    const formattedMessage = `${greeting} ${userName}! 👋\n\n${answer}${this.getFooter(isBusinessHours)}`;

    return {
      message: formattedMessage,
      confidence: 0.6,
      sources: [],
      isFromRAG: false,
      timestamp: new Date().toISOString()
    };
  }

  getFallbackResponses() {
    return {
      default: `Sou o assistente virtual da *Vereadora Rafaela de Nilda* 🏛️

Estou aqui para ajudar com informações sobre:
• 📋 Projetos de lei e propostas
• 🏥 Saúde pública em Parnamirim
• 🚌 Transporte público
• 🌳 Meio ambiente
• 📞 Contato com o gabinete

*Como posso ajudá-lo hoje?*`,

      keywords: {
        'projeto,lei,legislação': `📋 *Projetos de Lei da Vereadora Rafaela*

A Vereadora trabalha constantemente em projetos que beneficiem Parnamirim:

• Melhoria da saúde pública
• Transporte público eficiente  
• Proteção ambiental
• Direitos dos cidadãos

Para informações específicas sobre projetos em andamento, entre em contato com nosso gabinete.`,

        'horário,atendimento,funcionamento': `🕐 *Horário de Atendimento*

*Gabinete da Vereadora Rafaela de Nilda:*
📅 Segunda a Sexta: 8h às 17h
📍 Câmara Municipal de Parnamirim/RN

*Agendamentos:*
📞 ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}
📧 ${process.env.GABINETE_EMAIL || '<EMAIL>'}`,

        'saúde,hospital,posto': `🏥 *Saúde Pública em Parnamirim*

A Vereadora Rafaela atua na fiscalização e melhoria dos serviços de saúde:

• Acompanhamento das UBS
• Fiscalização de hospitais
• Propostas de melhorias
• Defesa do SUS

Para denúncias ou sugestões sobre saúde pública, entre em contato conosco.`,

        'transporte,ônibus,mobilidade': `🚌 *Transporte Público*

Acompanhamos de perto as questões de mobilidade urbana:

• Qualidade do transporte público
• Acessibilidade nos ônibus
• Novas linhas e horários
• Infraestrutura de paradas

Tem alguma reclamação ou sugestão sobre transporte? Nos informe!`
      }
    };
  }

  getGreeting() {
    const hour = new Date().getHours();
    
    if (hour >= 5 && hour < 12) {
      return 'Bom dia';
    } else if (hour >= 12 && hour < 18) {
      return 'Boa tarde';
    } else {
      return 'Boa noite';
    }
  }

  shouldIncludeGreeting(userInfo) {
    // Incluir saudação se for a primeira mensagem da conversa
    return !userInfo.hasGreeted;
  }

  isBusinessHours() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = domingo, 1 = segunda, etc.
    
    const startHour = parseInt(process.env.HORARIO_INICIO?.split(':')[0]) || 8;
    const endHour = parseInt(process.env.HORARIO_FIM?.split(':')[0]) || 18;
    const workDays = process.env.DIAS_FUNCIONAMENTO?.split(',').map(d => parseInt(d)) || [1,2,3,4,5];
    
    return workDays.includes(day) && hour >= startHour && hour < endHour;
  }

  getFooter(isBusinessHours) {
    const footer = `\n\n---\n🏛️ *Vereadora Rafaela de Nilda*\n📍 Câmara Municipal de Parnamirim/RN`;
    
    if (!isBusinessHours) {
      return footer + `\n\n⏰ *Fora do horário comercial*\nResponderemos em breve no próximo dia útil.`;
    }
    
    return footer + `\n\n📞 ${process.env.GABINETE_TELEFONE || '(84) 99999-9999'}`;
  }

  async uploadDocument(documentData) {
    try {
      this.logger.info('📄 Enviando documento para processamento RAG...');
      
      const response = await this.httpClient.post(`${this.ragApiUrl}/documents`, documentData);
      
      this.logger.info('✅ Documento enviado com sucesso');
      return response.data;
      
    } catch (error) {
      this.logger.error('❌ Erro ao enviar documento:', error);
      throw error;
    }
  }

  async getDocuments() {
    try {
      const response = await this.httpClient.get(`${this.ragApiUrl}/documents`);
      return response.data;
    } catch (error) {
      this.logger.error('❌ Erro ao obter documentos:', error);
      throw error;
    }
  }

  async getConversations() {
    try {
      const response = await this.httpClient.get(`${this.ragApiUrl}/conversations`);
      return response.data;
    } catch (error) {
      this.logger.error('❌ Erro ao obter conversas:', error);
      throw error;
    }
  }

  getStatus() {
    return {
      isInitialized: this.isInitialized,
      frontendUrl: this.frontendUrl,
      ragApiUrl: this.ragApiUrl,
      timestamp: new Date().toISOString()
    };
  }
}
