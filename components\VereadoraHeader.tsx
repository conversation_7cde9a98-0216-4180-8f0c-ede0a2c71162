import React from 'react';

export const VereadoraHeader: React.FC = () => {
  return (
    <header className="relative overflow-hidden">
      {/* Background com gradiente animado - Tema Escuro/Prateado/Alaranjado */}
      <div className="absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-orange-600 opacity-95"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-orange-300 to-transparent opacity-10 animate-pulse"></div>

      {/* Efeito de partículas */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-4 -left-4 w-72 h-72 bg-orange-400 opacity-10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-4 -right-4 w-96 h-96 bg-gray-300 opacity-15 rounded-full blur-3xl animate-pulse"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 py-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {/* Logo moderno com glass morphism */}
            <div className="relative group">
              <div className="w-16 h-16 glass-card flex items-center justify-center">
                <span className="text-2xl font-bold text-white transition-transform duration-300 group-hover:scale-110">RN</span>
              </div>
              <div className="absolute -inset-1 bg-gradient-to-r from-orange-400 to-gray-400 rounded-full blur opacity-30 group-hover:opacity-50 transition-opacity"></div>
            </div>

            {/* Título com animação */}
            <div className="animate-fade-in-up">
              <h1 className="text-3xl font-bold text-white mb-1 tracking-tight">
                Vereadora Rafaela de Nilda
              </h1>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                <p className="text-gray-200 font-medium">Assistente Virtual Inteligente</p>
              </div>
            </div>
          </div>

          {/* Info lateral com glass morphism */}
          <div className="hidden md:flex items-center space-x-6 animate-slide-in-right">
            <div className="text-right">
              <p className="text-sm text-gray-300 font-medium">Atendimento 24h</p>
              <p className="text-xl font-bold text-white">Inteligência Artificial</p>
              <div className="flex items-center justify-end space-x-1 mt-1">
                <div className="w-1 h-1 bg-orange-400 rounded-full"></div>
                <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                <div className="w-1 h-1 bg-orange-400 rounded-full"></div>
              </div>
            </div>

            {/* Status com efeito hover */}
            <div className="relative group">
              <div className="w-12 h-12 glass-card flex items-center justify-center transition-all duration-300 group-hover:scale-110">
                <svg className="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="absolute -inset-1 bg-gradient-to-r from-orange-400 to-gray-400 rounded-full blur opacity-0 group-hover:opacity-40 transition-opacity"></div>
            </div>
          </div>
        </div>

        {/* Barra de informações moderna */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3 px-4 py-2 glass-card animate-fade-in-up" style={{animationDelay: '0.1s'}}>
            <div className="w-8 h-8 bg-gradient-to-r from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <span className="text-white font-medium">Parnamirim/RN - Câmara Municipal</span>
          </div>

          <div className="flex items-center space-x-3 px-4 py-2 glass-card animate-fade-in-up" style={{animationDelay: '0.2s'}}>
            <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <span className="text-white font-medium"><EMAIL></span>
          </div>

          <div className="flex items-center space-x-3 px-4 py-2 glass-card animate-fade-in-up" style={{animationDelay: '0.3s'}}>
            <div className="w-8 h-8 bg-gradient-to-r from-gray-600 to-orange-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <span className="text-white font-medium">(84) 99999-9999</span>
          </div>
        </div>
      </div>
    </header>
  );
};
