import React, { useState, useEffect } from 'react';
import { whatsappService } from '../services/whatsappService';

interface Chat {
  id: string;
  name: string;
  isGroup: boolean;
  unreadCount: number;
  lastMessage: any;
  timestamp: number;
  archived: boolean;
  pinned: boolean;
  lastSync: string;
}

interface Message {
  id: string;
  body: string;
  type: string;
  timestamp: number;
  from: string;
  to: string;
  fromMe: boolean;
  hasMedia: boolean;
}

interface WhatsAppChatsProps {
  isConnected: boolean;
}

export const WhatsAppChats: React.FC<WhatsAppChatsProps> = ({ isConnected }) => {
  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [stats, setStats] = useState<any>(null);

  // Carregar conversas
  const loadChats = async () => {
    if (!isConnected) return;

    try {
      setLoading(true);
      const response = await whatsappService.getChats();
      console.log('📱 Resposta getChats:', response);

      // A resposta pode vir em diferentes formatos
      let chatsArray = [];
      if (response.data && Array.isArray(response.data)) {
        chatsArray = response.data;
      } else if (response.data && response.data.chats && Array.isArray(response.data.chats)) {
        chatsArray = response.data.chats;
      } else if (Array.isArray(response)) {
        chatsArray = response;
      }

      console.log('📱 Chats processados:', chatsArray);

      // Se não há chats e não está conectado, usar dados de exemplo para teste
      if (chatsArray.length === 0 && !isConnected) {
        const mockChats = [
          {
            id: 'mock-1',
            name: 'Exemplo - Cidadão',
            isGroup: false,
            unreadCount: 2,
            lastMessage: { body: 'Olá, preciso de informações sobre...' },
            timestamp: Date.now() / 1000,
            archived: false,
            pinned: false,
            lastSync: new Date().toISOString()
          },
          {
            id: 'mock-2',
            name: 'Exemplo - Grupo Vereadores',
            isGroup: true,
            unreadCount: 0,
            lastMessage: { body: 'Reunião marcada para amanhã' },
            timestamp: (Date.now() - 3600000) / 1000,
            archived: false,
            pinned: true,
            lastSync: new Date().toISOString()
          }
        ];
        setChats(mockChats);
      } else {
        setChats(chatsArray);
      }
    } catch (error) {
      console.error('Erro ao carregar conversas:', error);
      setChats([]); // Garantir que seja sempre um array
    } finally {
      setLoading(false);
    }
  };

  // Carregar mensagens de uma conversa
  const loadMessages = async (chatId: string) => {
    try {
      setLoading(true);
      const response = await whatsappService.getChatMessages(chatId);
      setMessages(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar mensagens:', error);
    } finally {
      setLoading(false);
    }
  };

  // Iniciar sincronização
  const startSync = async () => {
    try {
      setSyncing(true);
      await whatsappService.startSync();
      
      // Aguardar um pouco e recarregar
      setTimeout(() => {
        loadChats();
        loadStats();
      }, 3000);
      
    } catch (error) {
      console.error('Erro ao sincronizar:', error);
    } finally {
      setSyncing(false);
    }
  };

  // Carregar estatísticas
  const loadStats = async () => {
    if (!isConnected) return;
    
    try {
      const response = await whatsappService.getStats();
      setStats(response.data);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  // Selecionar conversa
  const selectChat = (chat: Chat) => {
    setSelectedChat(chat);
    loadMessages(chat.id);
  };

  // Formatar timestamp
  const formatTime = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString('pt-BR');
  };

  // Formatar nome do chat
  const formatChatName = (chat: Chat) => {
    if (chat.isGroup) {
      return `👥 ${chat.name}`;
    }
    return `👤 ${chat.name}`;
  };

  useEffect(() => {
    if (isConnected) {
      loadChats();
      loadStats();
    }
  }, [isConnected]);

  if (!isConnected) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-4">📱</div>
          <h3 className="text-lg font-medium text-gray-700 mb-2">WhatsApp não conectado</h3>
          <p className="text-gray-500">Conecte o WhatsApp para visualizar as conversas sincronizadas</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-800">📱 Conversas WhatsApp</h3>
            {stats && (
              <p className="text-sm text-gray-500">
                {stats.totalChats} conversas • {stats.totalMessages} mensagens
              </p>
            )}
          </div>
          <button
            onClick={startSync}
            disabled={syncing}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {syncing ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Sincronizando...</span>
              </>
            ) : (
              <>
                <span>🔄</span>
                <span>Sincronizar</span>
              </>
            )}
          </button>
        </div>
      </div>

      <div className="flex h-96">
        {/* Lista de conversas */}
        <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
          {loading && (!Array.isArray(chats) || chats.length === 0) ? (
            <div className="p-4 text-center text-gray-500">
              <div className="animate-spin w-6 h-6 border-2 border-gray-300 border-t-green-600 rounded-full mx-auto mb-2"></div>
              Carregando conversas...
            </div>
          ) : (!Array.isArray(chats) || chats.length === 0) ? (
            <div className="p-4 text-center text-gray-500">
              <div className="text-2xl mb-2">💬</div>
              <p>Nenhuma conversa sincronizada</p>
              <p className="text-xs mt-1">Clique em "Sincronizar" para carregar</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {Array.isArray(chats) && chats.map((chat) => (
                <div
                  key={chat.id}
                  onClick={() => selectChat(chat)}
                  className={`p-3 cursor-pointer hover:bg-gray-50 ${
                    selectedChat?.id === chat.id ? 'bg-green-50 border-r-2 border-green-500' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {formatChatName(chat)}
                      </p>
                      {chat.lastMessage && (
                        <p className="text-xs text-gray-500 truncate">
                          {chat.lastMessage.body || 'Mídia'}
                        </p>
                      )}
                    </div>
                    <div className="flex flex-col items-end">
                      {chat.unreadCount > 0 && (
                        <span className="bg-green-500 text-white text-xs rounded-full px-2 py-1 mb-1">
                          {chat.unreadCount}
                        </span>
                      )}
                      <span className="text-xs text-gray-400">
                        {formatTime(chat.timestamp)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Mensagens da conversa selecionada */}
        <div className="flex-1 flex flex-col">
          {selectedChat ? (
            <>
              {/* Header da conversa */}
              <div className="p-4 border-b border-gray-200 bg-gray-50">
                <h4 className="font-medium text-gray-800">{formatChatName(selectedChat)}</h4>
                <p className="text-sm text-gray-500">
                  {selectedChat.isGroup ? 'Grupo' : 'Conversa privada'} • 
                  Última sincronização: {new Date(selectedChat.lastSync).toLocaleString('pt-BR')}
                </p>
              </div>

              {/* Lista de mensagens */}
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {loading ? (
                  <div className="text-center text-gray-500">
                    <div className="animate-spin w-6 h-6 border-2 border-gray-300 border-t-green-600 rounded-full mx-auto mb-2"></div>
                    Carregando mensagens...
                  </div>
                ) : messages.length === 0 ? (
                  <div className="text-center text-gray-500">
                    <div className="text-2xl mb-2">💭</div>
                    <p>Nenhuma mensagem encontrada</p>
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.fromMe ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.fromMe
                            ? 'bg-green-500 text-white'
                            : 'bg-gray-200 text-gray-800'
                        }`}
                      >
                        {!message.fromMe && selectedChat.isGroup && (
                          <p className="text-xs font-medium mb-1 opacity-75">
                            {message.from.split('@')[0]}
                          </p>
                        )}
                        <p className="text-sm">
                          {message.hasMedia ? '📎 Mídia' : message.body}
                        </p>
                        <p className={`text-xs mt-1 ${
                          message.fromMe ? 'text-green-100' : 'text-gray-500'
                        }`}>
                          {formatTime(message.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-4">💬</div>
                <p>Selecione uma conversa para ver as mensagens</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
