import React from 'react';

interface TabNavigationProps {
  activeTab: 'chat' | 'history' | 'documents' | 'monitoring' | 'whatsapp';
  onTabChange: (tab: 'chat' | 'history' | 'documents' | 'monitoring' | 'whatsapp') => void;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    {
      id: 'chat' as const,
      name: 'Chat',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      description: 'Conversar com a IA'
    },
    {
      id: 'history' as const,
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      description: 'Conversas anteriores'
    },
    {
      id: 'documents' as const,
      name: 'Documentos',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      description: 'Gerenciar arquivos'
    },
    {
      id: 'monitoring' as const,
      name: 'Monitoramento',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      description: 'Estatísticas e métricas'
    },
    {
      id: 'whatsapp' as const,
      name: 'WhatsApp',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21l4-4 4 4M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
        </svg>
      ),
      description: 'Integração WhatsApp'
    }
  ];

  return (
    <nav className="relative bg-gradient-to-r from-white/80 to-white/60 backdrop-blur-sm border-b border-white/20">
      {/* Background decorativo */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-purple-50/50"></div>

      <div className="relative z-10 flex space-x-2 px-6 py-2">
        {tabs.map((tab, index) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`
              tab-button flex items-center space-x-3 py-3 px-6 rounded-xl font-medium text-sm transition-all duration-300 group
              ${activeTab === tab.id ? 'active' : ''}
            `}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className={`
              w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300
              ${activeTab === tab.id
                ? 'bg-white/20 text-white'
                : 'bg-white/10 text-gray-600 group-hover:bg-white/20 group-hover:text-white'
              }
            `}>
              {tab.icon}
            </div>

            <div className="text-left">
              <div className={`font-semibold ${activeTab === tab.id ? 'text-white' : 'text-gray-700'}`}>
                {tab.name}
              </div>
              <div className={`text-xs ${activeTab === tab.id ? 'text-white/80' : 'text-gray-500'}`}>
                {tab.description}
              </div>
            </div>

            {/* Indicador ativo */}
            {activeTab === tab.id && (
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full animate-scale-in"></div>
            )}

            {/* Efeito hover */}
            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
          </button>
        ))}
      </div>

      {/* Linha decorativa */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
    </nav>
  );
};
