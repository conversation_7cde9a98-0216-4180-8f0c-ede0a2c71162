import { create, Whatsapp } from '@wppconnect-team/wppconnect';
import QRCode from 'qrcode';
import { Logger } from '../utils/Logger.js';
import { SessionManager } from './SessionManager.js';
import { PersistenceService } from './PersistenceService.js';
import { AntiBanService } from './AntiBanService.js';
import fs from 'fs/promises';
import path from 'path';
import cron from 'node-cron';

export class WhatsAppService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.qrCode = null;
    this.sessionName = process.env.WHATSAPP_SESSION_NAME || 'vereadora-rafaela';
    this.logger = new Logger();
    this.sessionManager = new SessionManager();
    this.persistenceService = new PersistenceService();
    this.antiBanService = new AntiBanService();
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 5;

    // Configurações de reconexão
    this.reconnectInterval = null;
    this.reconnectDelay = 30000; // 30 segundos
    this.maxReconnectDelay = 300000; // 5 minutos
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;

    // Configurações de monitoramento
    this.healthCheckInterval = null;
    this.lastHeartbeat = null;
    this.connectionQuality = 'unknown';

    // Configurações de backup
    this.autoBackupEnabled = process.env.AUTO_BACKUP_ENABLED === 'true';
    this.backupInterval = parseInt(process.env.BACKUP_INTERVAL_HOURS) || 6;
    
    // Configurações da sessão
    this.sessionConfig = {
      session: this.sessionName,
      catchQR: (base64Qr, asciiQR) => this.handleQRCode(base64Qr, asciiQR),
      statusFind: (statusSession, session) => this.handleStatusChange(statusSession, session),
      headless: true,
      devtools: false,
      useChrome: true,
      debug: false,
      logQR: true,
      browserArgs: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ],
      autoClose: process.env.WHATSAPP_AUTO_CLOSE === 'true' ? 60000 : false,
      disableWelcome: process.env.WHATSAPP_DISABLE_WELCOME === 'true'
    };

    // Callbacks para eventos
    this.onQRCode = null;
    this.onStatusChange = null;
    this.onMessage = null;
    this.onReady = null;
    this.onDisconnected = null;
  }

  async initialize() {
    this.logger.info('🔄 Inicializando WhatsApp Service...');

    try {
      // Tentar carregar sessão persistida
      await this.loadPersistedSession();

      // Criar nova sessão
      await this.createSession();

      // Configurar monitoramento
      this.setupHealthMonitoring();

      // Configurar backup automático
      if (this.autoBackupEnabled) {
        this.setupAutoBackup();
      }

      return true;
    } catch (error) {
      this.logger.error('❌ Erro ao inicializar WhatsApp Service:', error);

      // Tentar reconexão automática
      this.scheduleReconnect();
      throw error;
    }
  }

  async createSession() {
    try {
      this.connectionAttempts++;
      this.logger.info(`📱 Criando sessão WhatsApp (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts})...`);

      this.client = await create(this.sessionConfig);
      
      // Configurar event listeners
      this.setupEventListeners();
      
      this.logger.info('✅ Sessão WhatsApp criada com sucesso!');
      return this.client;

    } catch (error) {
      this.logger.error('❌ Erro ao criar sessão WhatsApp:', error);
      
      if (this.connectionAttempts < this.maxConnectionAttempts) {
        this.logger.info(`🔄 Tentando reconectar em 5 segundos...`);
        setTimeout(() => this.createSession(), 5000);
      } else {
        this.logger.error('❌ Máximo de tentativas de conexão atingido');
        throw error;
      }
    }
  }

  setupEventListeners() {
    if (!this.client) {
      this.logger.warn('⚠️ Cliente não disponível para configurar event listeners');
      return;
    }

    this.logger.info('🔧 Configurando event listeners do WhatsApp...');

    // Evento de mensagem recebida
    this.client.onMessage(async (message) => {
      try {
        this.logger.debug('📨 Event listener: Nova mensagem recebida');
        await this.handleIncomingMessage(message);
      } catch (error) {
        this.logger.error('❌ Erro ao processar mensagem no event listener:', error);
      }
    });

    this.logger.info('✅ Event listener de mensagens configurado');

    // Evento de mudança de estado
    this.client.onStateChange((state) => {
      this.logger.info(`📱 Estado da conexão: ${state}`);

      if (state === 'CONNECTED') {
        this.isConnected = true;
        this.connectionAttempts = 0;
        this.qrCode = null;
        this.logger.info('🎉 WhatsApp conectado com sucesso!');

        // Reconfigurar event listeners após conexão
        this.logger.info('🔄 Reconfigurando event listeners após conexão...');
        this.setupEventListeners();

        // Salvar sessão após conexão bem-sucedida
        this.saveCurrentSession();

        // Iniciar sincronização de conversas
        this.startConversationSync();

        this.onReady && this.onReady();
      } else if (state === 'DISCONNECTED' || state === 'UNPAIRED') {
        this.isConnected = false;
        this.logger.warn('⚠️ WhatsApp desconectado');
        this.onDisconnected && this.onDisconnected();
      }
    });

    // Evento de interface para detectar quando está totalmente carregado
    this.client.onInterfaceChange((interfaceInfo) => {
      this.logger.info(`📱 Interface: ${interfaceInfo.displayInfo}`);

      if (interfaceInfo.displayInfo === 'MAIN' && this.isConnected) {
        this.logger.info('✅ WhatsApp totalmente carregado e pronto para uso!');
      }
    });

    // Evento de desconexão
    this.client.onIncomingCall(async (call) => {
      this.logger.info('📞 Chamada recebida:', call);
      // Rejeitar chamadas automaticamente
      await this.client.rejectCall(call.id);
    });
  }

  async handleQRCode(base64Qr, asciiQR) {
    // Evitar gerar QR Code se já estiver conectado
    if (this.isConnected) {
      this.logger.info('✅ WhatsApp já está conectado, ignorando QR Code');
      return;
    }

    this.logger.info(`📱 QR Code gerado (tentativa ${this.connectionAttempts + 1}/${this.maxConnectionAttempts})`);

    try {
      // Salvar QR Code como imagem
      const qrPath = path.join(process.cwd(), 'public', 'qr-code.png');
      await fs.mkdir(path.dirname(qrPath), { recursive: true });

      const qrBuffer = Buffer.from(base64Qr.replace('data:image/png;base64,', ''), 'base64');
      await fs.writeFile(qrPath, qrBuffer);

      this.qrCode = {
        base64: base64Qr,
        ascii: asciiQR,
        path: '/static/qr-code.png',
        timestamp: new Date().toISOString(),
        attempt: this.connectionAttempts + 1
      };

      // Callback personalizado
      this.onQRCode && this.onQRCode(this.qrCode);

      this.logger.info('✅ QR Code salvo em:', qrPath);
      console.log('\n📱 QR Code para WhatsApp (Tentativa ' + (this.connectionAttempts + 1) + '):\n');
      console.log(asciiQR);
      console.log('\n🌐 Ou acesse: http://localhost:' + (process.env.PORT || 3001) + '/static/qr-code.png\n');
      console.log('⏱️  QR Code expira em 20 segundos. Escaneie rapidamente!\n');

      // Definir timeout para o QR Code (20 segundos)
      setTimeout(() => {
        if (!this.isConnected && this.qrCode && this.qrCode.timestamp === this.qrCode.timestamp) {
          this.logger.warn('⏰ QR Code expirou, aguardando novo...');
        }
      }, 20000);

    } catch (error) {
      this.logger.error('❌ Erro ao processar QR Code:', error);
    }
  }

  async handleStatusChange(statusSession, session) {
    this.logger.info(`📱 Status da sessão: ${statusSession} - ${session}`);

    const statusMap = {
      'notLogged': 'Não logado - Escaneie o QR Code',
      'qrReadSuccess': 'QR Code lido com sucesso - Conectando...',
      'chatsAvailable': 'Chats disponíveis - Finalizando conexão...',
      'deviceNotConnected': 'Dispositivo não conectado',
      'serverWssNotConnected': 'Servidor WebSocket não conectado',
      'noOpenBrowser': 'Navegador não aberto',
      'phoneNotConnected': 'Telefone não conectado',
      'authenticated': 'Autenticado com sucesso',
      'authenticationFailure': 'Falha na autenticação',
      'ready': 'Pronto para uso',
      'inChat': 'Conectado e pronto',
      'isLogged': 'Logado com sucesso'
    };

    const friendlyStatus = statusMap[statusSession] || statusSession;
    this.logger.info(`📱 Status: ${friendlyStatus}`);

    // Tratar estados específicos
    if (statusSession === 'qrReadSuccess') {
      this.logger.info('🎉 QR Code escaneado! Finalizando conexão...');
      this.qrCode = null; // Limpar QR Code após leitura
    } else if (statusSession === 'inChat' || statusSession === 'isLogged') {
      this.logger.info('✅ WhatsApp totalmente conectado e pronto!');
      this.isConnected = true;
      this.qrCode = null;
      this.connectionAttempts = 0;
    } else if (statusSession === 'authenticationFailure') {
      this.logger.error('❌ Falha na autenticação - Gerando novo QR Code...');
      this.isConnected = false;
      this.connectionAttempts++;
    }

    // Callback personalizado
    this.onStatusChange && this.onStatusChange(statusSession, session, friendlyStatus);

    // Salvar status na sessão
    await this.sessionManager.updateSessionStatus(session, statusSession, friendlyStatus);
  }

  async handleIncomingMessage(message) {
    try {
      // Log detalhado da mensagem recebida
      this.logger.info(`📨 Mensagem recebida de ${message.from}: "${message.body || '[sem texto]'}"`);
      this.logger.debug('📋 Detalhes da mensagem:', {
        from: message.from,
        to: message.to,
        body: message.body,
        type: message.type,
        fromMe: message.fromMe,
        isGroupMsg: message.isGroupMsg,
        timestamp: message.timestamp,
        hasMedia: message.hasMedia
      });

      // Filtrar mensagens próprias
      if (message.fromMe) {
        this.logger.debug('📤 Mensagem própria ignorada');
        return;
      }

      // Filtrar mensagens de grupos (se não permitido)
      if (message.isGroupMsg && !process.env.ALLOW_GROUP_MESSAGES) {
        this.logger.debug('👥 Mensagem de grupo ignorada (grupos desabilitados)');
        return;
      }

      // Marcar como lida
      try {
        await this.client.sendSeen(message.from);
        this.logger.debug('✅ Mensagem marcada como lida');
      } catch (seenError) {
        this.logger.warn('⚠️ Erro ao marcar mensagem como lida:', seenError.message);
      }

      // Verificar se há callback configurado
      if (this.onMessage) {
        this.logger.debug('🔄 Chamando callback de mensagem...');
        await this.onMessage(message);
        this.logger.debug('✅ Callback de mensagem executado');
      } else {
        this.logger.warn('⚠️ Nenhum callback de mensagem configurado!');
      }

    } catch (error) {
      this.logger.error('❌ Erro ao processar mensagem recebida:', {
        error: error.message,
        stack: error.stack,
        messageFrom: message?.from,
        messageBody: message?.body
      });
    }
  }

  async sendMessage(to, message, options = {}) {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      this.logger.info(`📤 Preparando envio para ${to}: ${message.substring(0, 50)}...`);

      // Verificar se o número é válido
      const isValidNumber = await this.client.checkNumberStatus(to);
      if (!isValidNumber.canReceiveMessage) {
        throw new Error('Número não pode receber mensagens');
      }

      // Usar sistema anti-ban para envio seguro
      const result = await this.antiBanService.processMessageSafely(this.client, to, message, options);

      if (!result.success) {
        throw new Error(`Anti-ban bloqueou envio: ${result.reason} (risco: ${result.riskLevel}%)`);
      }

      this.logger.info(`✅ Mensagem enviada com segurança: ${result.messageId} (risco: ${result.riskLevel}%)`);
      return {
        id: result.messageId,
        riskLevel: result.riskLevel,
        delay: result.delay,
        antiBanActive: true
      };

    } catch (error) {
      this.logger.error('❌ Erro ao enviar mensagem:', error);
      throw error;
    }
  }

  async sendImage(to, imagePath, caption = '', options = {}) {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      const result = await this.client.sendImage(to, imagePath, 'image', caption, options);
      this.logger.info(`📤 Imagem enviada para ${to}`);
      return result;

    } catch (error) {
      this.logger.error('❌ Erro ao enviar imagem:', error);
      throw error;
    }
  }

  async sendDocument(to, documentPath, filename, caption = '', options = {}) {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      const result = await this.client.sendFile(to, documentPath, filename, caption, options);
      this.logger.info(`📤 Documento enviado para ${to}: ${filename}`);
      return result;

    } catch (error) {
      this.logger.error('❌ Erro ao enviar documento:', error);
      throw error;
    }
  }

  async getChats() {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      const chats = await this.client.getAllChats();
      return chats;

    } catch (error) {
      this.logger.error('❌ Erro ao obter chats:', error);
      throw error;
    }
  }

  async getContacts() {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      const contacts = await this.client.getAllContacts();
      return contacts;

    } catch (error) {
      this.logger.error('❌ Erro ao obter contatos:', error);
      throw error;
    }
  }

  async getContact(contactId) {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      const contact = await this.client.getContact(contactId);
      return contact;

    } catch (error) {
      this.logger.error(`❌ Erro ao obter contato ${contactId}:`, error);
      return null;
    }
  }

  async getContacts() {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      const contacts = await this.client.getAllContacts();
      return contacts;

    } catch (error) {
      this.logger.error('❌ Erro ao obter contatos:', error);
      throw error;
    }
  }

  getConnectionStatus() {
    let status = 'Desconectado';
    let statusCode = 'disconnected';

    if (this.isConnected) {
      status = 'Conectado';
      statusCode = 'connected';
    } else if (this.qrCode) {
      status = 'Aguardando QR Code';
      statusCode = 'waiting_qr';
    } else if (this.connectionAttempts > 0) {
      status = 'Conectando...';
      statusCode = 'connecting';
    }

    return {
      isConnected: this.isConnected,
      sessionName: this.sessionName,
      qrCode: this.qrCode,
      hasQRCode: !!this.qrCode,
      qrCodeUrl: this.qrCode ? '/static/qr-code.png' : null,
      connectionAttempts: this.connectionAttempts,
      maxAttempts: this.maxConnectionAttempts,
      status,
      statusCode,
      timestamp: new Date().toISOString()
    };
  }

  async stop() {
    try {
      this.logger.info('🛑 Parando WhatsApp Service...');
      
      if (this.client) {
        await this.client.close();
        this.client = null;
      }
      
      this.isConnected = false;
      this.qrCode = null;
      
      this.logger.info('✅ WhatsApp Service parado com sucesso');

    } catch (error) {
      this.logger.error('❌ Erro ao parar WhatsApp Service:', error);
      throw error;
    }
  }

  // Métodos para configurar callbacks
  setOnQRCode(callback) {
    this.onQRCode = callback;
  }

  setOnStatusChange(callback) {
    this.onStatusChange = callback;
  }

  setOnMessage(callback) {
    this.onMessage = callback;
  }

  setOnReady(callback) {
    this.onReady = callback;
  }

  setOnDisconnected(callback) {
    this.onDisconnected = callback;
  }

  // ===== MÉTODOS DE PERSISTÊNCIA =====

  async loadPersistedSession() {
    try {
      this.logger.info('📂 Tentando carregar sessão persistida...');

      const sessionData = await this.persistenceService.loadWhatsAppSession(this.sessionName);

      if (sessionData) {
        this.logger.info('✅ Sessão persistida carregada com sucesso');
        // Aplicar dados da sessão se necessário
        return sessionData;
      } else {
        this.logger.info('ℹ️ Nenhuma sessão persistida encontrada');
        return null;
      }
    } catch (error) {
      this.logger.warn('⚠️ Erro ao carregar sessão persistida:', error);
      return null;
    }
  }

  async saveCurrentSession() {
    try {
      if (!this.client || !this.isConnected) {
        return false;
      }

      const sessionData = {
        sessionName: this.sessionName,
        isConnected: this.isConnected,
        connectionTime: new Date().toISOString(),
        connectionQuality: this.connectionQuality,
        // Adicionar outros dados relevantes da sessão
      };

      const success = await this.persistenceService.saveWhatsAppSession(this.sessionName, sessionData);

      if (success) {
        this.logger.info('💾 Sessão atual salva com sucesso');
      }

      return success;
    } catch (error) {
      this.logger.error('❌ Erro ao salvar sessão atual:', error);
      return false;
    }
  }

  // ===== MÉTODOS DE RECONEXÃO =====

  scheduleReconnect() {
    if (this.reconnectInterval) {
      clearTimeout(this.reconnectInterval);
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('❌ Máximo de tentativas de reconexão atingido');
      return;
    }

    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts),
      this.maxReconnectDelay
    );

    this.logger.info(`🔄 Agendando reconexão em ${delay / 1000}s (tentativa ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);

    this.reconnectInterval = setTimeout(async () => {
      await this.attemptReconnect();
    }, delay);
  }

  async attemptReconnect() {
    try {
      this.reconnectAttempts++;
      this.logger.info(`🔄 Tentativa de reconexão ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      // Limpar cliente anterior
      if (this.client) {
        try {
          await this.client.close();
        } catch (error) {
          // Ignorar erros ao fechar cliente anterior
        }
        this.client = null;
      }

      // Tentar criar nova sessão
      await this.createSession();

      // Se chegou aqui, reconexão foi bem-sucedida
      this.reconnectAttempts = 0;
      this.logger.info('✅ Reconexão bem-sucedida!');

    } catch (error) {
      this.logger.error(`❌ Falha na reconexão (tentativa ${this.reconnectAttempts}):`, error);

      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      } else {
        this.logger.error('❌ Todas as tentativas de reconexão falharam');
      }
    }
  }

  // ===== MÉTODOS DE MONITORAMENTO =====

  setupHealthMonitoring() {
    // Verificação de saúde a cada 30 segundos
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 30000);

    this.logger.info('💓 Monitoramento de saúde configurado');
  }

  async performHealthCheck() {
    try {
      if (!this.client || !this.isConnected) {
        this.connectionQuality = 'disconnected';
        return;
      }

      const startTime = Date.now();

      // Tentar obter informações básicas
      const info = await this.client.getHostDevice();

      const responseTime = Date.now() - startTime;
      this.lastHeartbeat = new Date();

      // Determinar qualidade da conexão baseada no tempo de resposta
      if (responseTime < 1000) {
        this.connectionQuality = 'excellent';
      } else if (responseTime < 3000) {
        this.connectionQuality = 'good';
      } else if (responseTime < 5000) {
        this.connectionQuality = 'fair';
      } else {
        this.connectionQuality = 'poor';
      }

      this.logger.debug(`💓 Health check OK (${responseTime}ms) - Quality: ${this.connectionQuality}`);

    } catch (error) {
      this.connectionQuality = 'error';
      this.logger.warn('⚠️ Health check failed:', error.message);

      // Se múltiplas falhas consecutivas, tentar reconectar
      if (this.isConnected) {
        this.logger.warn('🔄 Conexão instável detectada, iniciando reconexão...');
        this.isConnected = false;
        this.scheduleReconnect();
      }
    }
  }

  // ===== MÉTODOS DE BACKUP =====

  setupAutoBackup() {
    // Backup automático baseado no intervalo configurado
    const cronExpression = `0 */${this.backupInterval} * * *`;

    cron.schedule(cronExpression, async () => {
      await this.createBackup();
    });

    this.logger.info(`💾 Backup automático configurado: a cada ${this.backupInterval} horas`);
  }

  async createBackup() {
    try {
      this.logger.info('💾 Iniciando backup automático...');

      // Salvar sessão atual
      await this.saveCurrentSession();

      // Criar backup via PersistenceService
      const result = await this.persistenceService.createBackup();

      if (result.success) {
        this.logger.info(`✅ Backup criado: ${result.sessionsCount} sessões`);
      } else {
        this.logger.error('❌ Falha no backup:', result.error);
      }

      // Limpeza de backups antigos
      await this.persistenceService.cleanupOldBackups(30);

      return result;
    } catch (error) {
      this.logger.error('❌ Erro no backup automático:', error);
      return { success: false, error: error.message };
    }
  }

  // ===== MÉTODOS DE STATUS AVANÇADO =====

  getAdvancedStatus() {
    return {
      ...this.getConnectionStatus(),
      reconnection: {
        attempts: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
        nextReconnectIn: this.reconnectInterval ? 'scheduled' : 'none',
        autoReconnectEnabled: true
      },
      health: {
        quality: this.connectionQuality,
        lastHeartbeat: this.lastHeartbeat,
        monitoringActive: !!this.healthCheckInterval
      },
      persistence: {
        autoBackupEnabled: this.autoBackupEnabled,
        backupInterval: this.backupInterval,
        lastBackup: null // Será preenchido pelo PersistenceService
      },
      security: {
        encryptionEnabled: true,
        sessionPersisted: true,
        integrityChecks: true
      },
      antiBan: this.antiBanService.getAntiBanStats()
    };
  }

  // ===== SINCRONIZAÇÃO DE CONVERSAS =====

  // Método para sincronizar conversas
  async startConversationSync() {
    if (!this.client || !this.isConnected) {
      this.logger.warn('⚠️ Cliente não conectado para sincronização');
      return;
    }

    try {
      this.logger.info('🔄 Iniciando sincronização de conversas...');

      // Obter todas as conversas (usando método atualizado)
      const chats = await this.client.listChats();
      this.logger.info(`📱 Encontradas ${chats.length} conversas`);

      // Processar conversas em lotes para não sobrecarregar
      const batchSize = 5;
      for (let i = 0; i < chats.length; i += batchSize) {
        const batch = chats.slice(i, i + batchSize);
        await this.processChatBatch(batch);

        // Pausa entre lotes
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      this.logger.info('✅ Sincronização de conversas concluída');

    } catch (error) {
      this.logger.error('❌ Erro na sincronização de conversas:', error);
    }
  }

  // Processar lote de conversas
  async processChatBatch(chats) {
    for (const chat of chats) {
      try {
        await this.syncSingleChat(chat);
      } catch (error) {
        const chatId = chat?.id?._serialized || 'ID_DESCONHECIDO';
        const chatName = chat?.name || chat?.pushname || 'Nome_Desconhecido';
        this.logger.warn(`⚠️ Erro ao sincronizar chat ${chatId} (${chatName}):`, {
          error: error.message,
          stack: error.stack,
          chatInfo: {
            id: chatId,
            name: chatName,
            isGroup: chat?.isGroup,
            hasId: !!chat?.id,
            hasMessages: !!chat?.fetchMessages
          }
        });
      }
    }
  }

  // Sincronizar uma conversa específica
  async syncSingleChat(chat) {
    try {
      // Validar se o chat tem estrutura necessária
      if (!chat || !chat.id || !chat.id._serialized) {
        throw new Error('Chat inválido: estrutura de ID ausente');
      }

      // Obter informações básicas do chat
      let chatName = chat.name || chat.pushname || 'Sem nome';

      // Se não tem nome e não é grupo, tentar obter do contato
      if (chatName === 'Sem nome' && !chat.isGroup) {
        try {
          const contact = await this.client.getContact(chat.id._serialized);
          if (contact && (contact.name || contact.pushname)) {
            chatName = contact.name || contact.pushname;
            this.logger.debug(`📞 Nome do contato obtido: ${chatName} para ${chat.id._serialized}`);
          }
        } catch (contactError) {
          this.logger.debug(`⚠️ Não foi possível obter informações do contato ${chat.id._serialized}:`, contactError.message);
        }
      }

      const chatInfo = {
        id: chat.id._serialized,
        name: chatName,
        isGroup: chat.isGroup || false,
        unreadCount: chat.unreadCount || 0,
        lastMessage: chat.lastMessage || null,
        timestamp: chat.timestamp || Date.now(),
        archived: chat.archived || false,
        pinned: chat.pinned || false,
        muteExpiration: chat.muteExpiration || null
      };

      // Obter mensagens usando a API correta do WPPConnect
      let messages = [];
      try {
        // Listar métodos disponíveis no cliente para debug
        this.logger.debug(`🔍 Métodos disponíveis no cliente:`, Object.getOwnPropertyNames(this.client).filter(name => name.includes('message') || name.includes('Message') || name.includes('chat') || name.includes('Chat')));

        // Tentar diferentes métodos do WPPConnect
        if (typeof this.client.getChatMessages === 'function') {
          messages = await this.client.getChatMessages(chatInfo.id, false, 50);
          this.logger.debug(`📨 Obtidas ${messages.length} mensagens (getChatMessages) para ${chatInfo.name}`);
        } else if (typeof this.client.getMessages === 'function') {
          messages = await this.client.getMessages(chatInfo.id, false, 50);
          this.logger.debug(`📨 Obtidas ${messages.length} mensagens (getMessages) para ${chatInfo.name}`);
        } else if (typeof this.client.getAllMessagesInChat === 'function') {
          messages = await this.client.getAllMessagesInChat(chatInfo.id, false, 50);
          this.logger.debug(`📨 Obtidas ${messages.length} mensagens (getAllMessagesInChat) para ${chatInfo.name}`);
        } else {
          this.logger.warn(`⚠️ Nenhum método de obter mensagens encontrado para ${chatInfo.name}`);
          // Salvar apenas as informações do chat sem mensagens
          await this.saveConversationData(chatInfo, []);
          return;
        }
      } catch (fetchError) {
        this.logger.warn(`⚠️ Erro ao buscar mensagens do chat ${chatInfo.name}:`, fetchError.message);
        // Salvar apenas as informações do chat sem mensagens
        await this.saveConversationData(chatInfo, []);
        return;
      }

      const processedMessages = messages.map(msg => {
        try {
          return {
            id: msg.id?._serialized || `msg_${Date.now()}_${Math.random()}`,
            body: msg.body || '',
            type: msg.type || 'text',
            timestamp: msg.timestamp || Date.now(),
            from: msg.from || '',
            to: msg.to || '',
            fromMe: msg.fromMe || false,
            hasMedia: msg.hasMedia || false,
            isForwarded: msg.isForwarded || false,
            isStatus: msg.isStatus || false,
            deviceType: msg.deviceType || 'unknown'
          };
        } catch (msgError) {
          this.logger.warn(`⚠️ Erro ao processar mensagem individual:`, msgError.message);
          return null;
        }
      }).filter(msg => msg !== null);

      // Salvar no banco de dados
      await this.saveConversationData(chatInfo, processedMessages);

      this.logger.debug(`💾 Chat sincronizado: ${chatInfo.name} (${processedMessages.length} mensagens)`);

    } catch (error) {
      const chatId = chat?.id?._serialized || 'ID_DESCONHECIDO';
      const chatName = chat?.name || chat?.pushname || 'Nome_Desconhecido';
      this.logger.warn(`⚠️ Erro ao processar chat individual ${chatId} (${chatName}):`, {
        error: error.message,
        stack: error.stack?.split('\n')[0] // Apenas primeira linha do stack
      });
      throw error; // Re-throw para ser capturado pelo processChatBatch
    }
  }

  // Salvar dados da conversa
  async saveConversationData(chatInfo, messages) {
    try {
      // Validar dados antes de salvar
      if (!chatInfo || !chatInfo.id) {
        throw new Error('Informações do chat inválidas');
      }

      // Salvar informações do chat
      await this.sessionManager.saveChat(this.sessionName, chatInfo);

      // Salvar mensagens se existirem
      if (messages && messages.length > 0) {
        await this.sessionManager.saveChatMessages(chatInfo.id, messages);
      }

    } catch (error) {
      this.logger.error(`❌ Erro ao salvar dados da conversa ${chatInfo?.name || 'DESCONHECIDA'}:`, {
        error: error.message,
        chatId: chatInfo?.id,
        messagesCount: messages?.length || 0
      });
      throw error; // Re-throw para ser tratado pelo método chamador
    }
  }

  // Obter conversas sincronizadas
  async getSyncedChats() {
    try {
      return await this.sessionManager.getChats(this.sessionName);
    } catch (error) {
      this.logger.error('❌ Erro ao obter conversas:', error);
      return [];
    }
  }

  // Obter mensagens de uma conversa
  async getChatMessages(chatId, limit = 100) {
    try {
      return await this.sessionManager.getChatMessages(chatId, limit);
    } catch (error) {
      this.logger.error('❌ Erro ao obter mensagens:', error);
      return [];
    }
  }

  // Sincronizar mensagens históricas de um chat específico
  async syncChatHistory(chatId, messageLimit = 100) {
    try {
      if (!this.isConnected || !this.client) {
        throw new Error('WhatsApp não está conectado');
      }

      this.logger.info(`🔄 Sincronizando histórico do chat ${chatId}...`);

      // Buscar o chat pelo ID
      const chats = await this.client.listChats();
      const targetChat = chats.find(chat => chat.id._serialized === chatId);

      if (!targetChat) {
        throw new Error('Chat não encontrado');
      }

      // Buscar mais mensagens históricas usando a API correta
      let messages = [];
      try {
        messages = await this.client.getChatMessages(chatId, false, messageLimit);
        this.logger.debug(`📨 Obtidas ${messages.length} mensagens históricas para ${chatId}`);
      } catch (fetchError) {
        // Tentar método alternativo
        try {
          messages = await this.client.getMessages(chatId, false, messageLimit);
          this.logger.debug(`📨 Obtidas ${messages.length} mensagens históricas (método alternativo) para ${chatId}`);
        } catch (altError) {
          throw new Error(`Não foi possível obter mensagens: ${altError.message}`);
        }
      }

      const processedMessages = messages.map(msg => {
        try {
          return {
            id: msg.id?._serialized || `msg_${Date.now()}_${Math.random()}`,
            body: msg.body || '',
            type: msg.type || 'text',
            timestamp: msg.timestamp || Date.now(),
            from: msg.from || '',
            to: msg.to || '',
            fromMe: msg.fromMe || false,
            hasMedia: msg.hasMedia || false,
            isForwarded: msg.isForwarded || false,
            isStatus: msg.isStatus || false,
            deviceType: msg.deviceType || 'unknown'
          };
        } catch (msgError) {
          this.logger.warn(`⚠️ Erro ao processar mensagem individual:`, msgError.message);
          return null;
        }
      }).filter(msg => msg !== null);

      // Salvar mensagens históricas
      await this.sessionManager.saveChatMessages(chatId, processedMessages);

      this.logger.info(`✅ Histórico sincronizado: ${processedMessages.length} mensagens para ${chatId}`);

      return {
        success: true,
        messagesCount: processedMessages.length,
        chatId
      };

    } catch (error) {
      this.logger.error(`❌ Erro ao sincronizar histórico do chat ${chatId}:`, error);
      return {
        success: false,
        error: error.message,
        chatId
      };
    }
  }

  // ===== LIMPEZA E PARADA =====

  async stop() {
    try {
      this.logger.info('🛑 Parando WhatsApp Service...');

      // Parar intervalos
      if (this.reconnectInterval) {
        clearTimeout(this.reconnectInterval);
        this.reconnectInterval = null;
      }

      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      // Salvar sessão antes de parar
      if (this.isConnected) {
        await this.saveCurrentSession();
      }

      // Fechar cliente
      if (this.client) {
        await this.client.close();
        this.client = null;
      }

      this.isConnected = false;
      this.qrCode = null;

      this.logger.info('✅ WhatsApp Service parado com sucesso');

    } catch (error) {
      this.logger.error('❌ Erro ao parar WhatsApp Service:', error);
      throw error;
    }
  }
}
